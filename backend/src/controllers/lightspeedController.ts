import { Request, Response } from 'express';
import { PrismaClient } from '@prisma/client';
import { withAccelerate } from '@prisma/extension-accelerate';
import logger from '../utils/logger';
// Placeholder for createLightspeedClient; replace with actual import if available
const { createLightspeedClient } = require('../lightspeedClient');

const prisma = new PrismaClient().$extends(withAccelerate());

// Public health endpoint - basic connectivity check only
export const getPublicLightspeedHealth = async (req: Request, res: Response) => {
  let lightspeedConnection = 'OK';
  let lightspeedApiError = null;

  try {
    // Use personal access token for health checks
    const LS_PERSONAL_ACCESS_TOKEN = process.env.LS_PERSONAL_ACCESS_TOKEN;
    const LS_DOMAIN = process.env.LS_DOMAIN;

    if (!LS_PERSONAL_ACCESS_TOKEN || !LS_DOMAIN) {
      throw new Error('Missing Lightspeed credentials (LS_PERSONAL_ACCESS_TOKEN or LS_DOMAIN)');
    }

    // Create a simple axios client for health check
    const axios = require('axios');
    const healthClient = axios.create({
      baseURL: `https://${LS_DOMAIN}.retail.lightspeed.app/api/2.0`,
      headers: {
        'Authorization': `Bearer ${LS_PERSONAL_ACCESS_TOKEN}`,
        'Content-Type': 'application/json',
      },
      timeout: 10000, // 10 second timeout for health checks
    });

    const retailerResponse = await healthClient.get('/retailer');
    logger.info('[Public Lightspeed Health] API connectivity check successful.', {
      retailer: retailerResponse.data?.data?.name || 'Unknown',
      timestamp: new Date().toISOString()
    });
  } catch (error: any) {
    lightspeedConnection = 'ERROR';
    lightspeedApiError = error.response?.data?.message || error.message || 'An unknown API error occurred.';
    logger.error(`[Public Lightspeed Health] API connectivity check failed: ${lightspeedApiError}`, {
      status: error.response?.status,
      timestamp: new Date().toISOString()
    });
  }

  // Return basic health status
  res.json({
    lightspeedConnection,
    lightspeedApiError,
    syncStatuses: [], // No sync status in public endpoint
  });
};

// Admin-only detailed health endpoint
export const getLightspeedHealth = async (req: Request, res: Response) => {
  let lightspeedConnection = 'OK';
  let lightspeedApiError = null;

  try {
    // Use personal access token for health checks instead of user session
    const LS_PERSONAL_ACCESS_TOKEN = process.env.LS_PERSONAL_ACCESS_TOKEN;
    const LS_DOMAIN = process.env.LS_DOMAIN;

    if (!LS_PERSONAL_ACCESS_TOKEN || !LS_DOMAIN) {
      throw new Error('Missing Lightspeed credentials (LS_PERSONAL_ACCESS_TOKEN or LS_DOMAIN)');
    }

    // Create a simple axios client for health check
    const axios = require('axios');
    const healthClient = axios.create({
      baseURL: `https://${LS_DOMAIN}.retail.lightspeed.app/api/2.0`,
      headers: {
        'Authorization': `Bearer ${LS_PERSONAL_ACCESS_TOKEN}`,
        'Content-Type': 'application/json',
      },
      timeout: 10000, // 10 second timeout for health checks
    });

    const retailerResponse = await healthClient.get('/retailer');
    logger.info('[Lightspeed Health] API connectivity check successful.', {
      retailer: retailerResponse.data?.data?.name || 'Unknown',
      timestamp: new Date().toISOString()
    });
  } catch (error: any) {
    lightspeedConnection = 'ERROR';
    lightspeedApiError = error.response?.data?.message || error.message || 'An unknown API error occurred.';
    logger.error(`[Lightspeed Health] API connectivity check failed: ${lightspeedApiError}`, {
      status: error.response?.status,
      timestamp: new Date().toISOString()
    });
  }
  try {
    const statuses = await prisma.syncStatus.findMany({ orderBy: { resource: 'asc' } });
    const sanitizedStatuses = statuses.map(s => ({
      ...s,
      lastSyncedVersion: s.lastSyncedVersion?.toString() || null,
      lastSyncedAt: s.lastSyncedAt?.toISOString() || null,
      createdAt: s.createdAt?.toISOString() || null,
      updatedAt: s.updatedAt?.toISOString() || null,
    }));
    res.status(200).json({
      lightspeedConnection,
      lightspeedApiError,
      syncStatuses: sanitizedStatuses,
    });
  } catch (dbError: any) {
    logger.error('[Lightspeed Health] Failed to get sync statuses from DB:', {
      message: dbError.message,
      stack: dbError.stack,
    });
    res.status(500).json({
      lightspeedConnection,
      lightspeedApiError,
      syncStatuses: [],
      databaseError: 'Failed to retrieve sync statuses from the database.'
    });
  }
};

export const deleteLightspeedUserSessions = async (req: Request, res: Response) => {
  const { userId } = req.params;
  try {
    const lightspeedClient = createLightspeedClient(null);
    await lightspeedClient.delete(`/users/${userId}/sessions`);
    logger.info(`[Lightspeed] Deleted all sessions for user ${userId}`);
    res.status(204).send();
  } catch (error: any) {
    logger.error(`[Lightspeed] Failed to delete sessions for user ${userId}:`, error.response?.data || error.message);
    res.status(500).json({ error: 'Failed to delete user sessions', details: error.response?.data || error.message });
  }
}; 