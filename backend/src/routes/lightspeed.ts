import express from 'express';
import { getLightspeedHealth, deleteLightspeedUserSessions, getPublicLightspeedHealth } from '../controllers/lightspeedController';
import { authMiddleware, requireAdmin } from '../middleware/auth';
import { asyncHandler } from '../utils/asyncHandler';

const router = express.Router();

// Public health endpoint (no authentication required)
router.get('/health', asyncHandler(getPublicLightspeedHealth));

// Admin-only detailed health endpoint
router.get('/health/admin', authMiddleware, requireAdmin, asyncHandler(getLightspeedHealth));

router.delete('/users/:userId/sessions', authMiddleware, requireAdmin, asyncHandler(deleteLightspeedUserSessions));

export default router; 